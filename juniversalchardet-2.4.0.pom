<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.github.albfernandez</groupId>
	<artifactId>juniversalchardet</artifactId>
	<version>2.4.0</version>
	<url>https://github.com/albfernandez/juniversalchardet</url>
	<packaging>jar</packaging>
	<name>juniversalchardet</name>
	<description>JUniversalChardet is a Java encoding detector library</description>

	<licenses>
		<license>
			<name>Mozilla Public License Version 1.1</name>
			<url>https://www.mozilla.org/en-US/MPL/1.1/</url>
		</license>
		<license>
			<name>GENERAL PUBLIC LICENSE, version 3 (GPL-3.0)</name>
			<url>http://www.gnu.org/licenses/gpl.txt</url>
		</license>
		<license>
			<name>GNU LESSER GENERAL PUBLIC LICENSE, version 3 (LGPL-3.0)</name>
			<url>http://www.gnu.org/licenses/lgpl.txt</url>
		</license>
	</licenses>
	<developers>
		<developer>
			<name>Alberto Fernández</name>
			<email><EMAIL></email>
			<organization>Alberto Fernández</organization>
			<organizationUrl>https://github.com/albfernandez/</organizationUrl>
		</developer>
	</developers>
	<scm>
		<connection>scm:git:**************:albfernandez/juniversalchardet.git</connection>
		<developerConnection>scm:git:**************:albfernandez/juniversalchardet.git</developerConnection>
		<url>**************:albfernandez/juniversalchardet.git</url>
	</scm>
	<issueManagement>
		<system>GitHub</system>
		<url>https://github.com/albfernandez/juniversalchardet/issues</url>
	</issueManagement>

	<properties>
		<compiler.level>1.7</compiler.level>
		<compiler.module.level>9</compiler.module.level>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

		<version.spotbugs-maven-plugin>4.1.4</version.spotbugs-maven-plugin>
		<version.findsecbugs-plugin>1.11.0</version.findsecbugs-plugin>
		<version.sb-contrib>7.4.7</version.sb-contrib>
	</properties>


	<dependencies>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>4.13.1</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>commons-codec</groupId>
			<artifactId>commons-codec</artifactId>
			<version>1.10</version>
			<scope>test</scope>
		</dependency>
	</dependencies>


	<build>
		<plugins>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<version>3.0.2</version>
				<configuration>
					<archive>
						<manifestEntries>
							<Automatic-Module-Name>com.github.albfernandez.juniversalchardet</Automatic-Module-Name>
						</manifestEntries>
						<manifest>
							<addDefaultImplementationEntries>true</addDefaultImplementationEntries>
							<addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
						</manifest>
					</archive>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-source-plugin</artifactId>
				<version>3.0.1</version>
				<executions>
					<execution>
						<id>attach-sources</id>
						<goals>
							<goal>jar</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-javadoc-plugin</artifactId>
				<version>3.2.0</version>
				<executions>
					<execution>
						<id>attach-javadocs</id>
						<goals>
							<goal>jar</goal>
						</goals>
						<configuration>
							<source>${compiler.level}</source>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-gpg-plugin</artifactId>
				<version>1.6</version>
				<configuration>
					<keyname>${gpg.keyname}</keyname>
					<passphraseServerId>${gpg.keyname}</passphraseServerId>
				</configuration>
				<executions>
					<execution>
						<id>sign-artifacts</id>
						<phase>verify</phase>
						<goals>
							<goal>sign</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.sonatype.plugins</groupId>
				<artifactId>nexus-staging-maven-plugin</artifactId>
				<version>1.6.6</version>
				<extensions>true</extensions>
				<configuration>
					<serverId>ossrh</serverId>
					<nexusUrl>https://oss.sonatype.org/</nexusUrl>
					<autoReleaseAfterClose>true</autoReleaseAfterClose>
				</configuration>
			</plugin>
		</plugins>
	</build>


	<profiles>
		<profile>
			<id>jdk7on</id>
			<activation>
				<jdk>(,1.8]</jdk>
			</activation>
			<build>
				<plugins>
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-compiler-plugin</artifactId>
						<version>3.7.0</version>
						<configuration>
							<excludes>
								<exclude>module-info.java</exclude>
							</excludes>
							<source>${compiler.level}</source>
							<target>${compiler.level}</target>
						</configuration>
					</plugin>
					<plugin>
						<groupId>org.codehaus.mojo</groupId>
						<artifactId>findbugs-maven-plugin</artifactId>
						<version>3.0.5</version>
						<executions>
							<execution>
								<id>findbugs-check</id>
								<phase>verify</phase>
								<goals>
									<goal>check</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>
		<profile>
			<id>jdk9on</id>
			<activation>
				<jdk>[9,)</jdk>
			</activation>
			<build>
				<plugins>
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-compiler-plugin</artifactId>
						<version>3.8.1</version>
						<executions>
							<execution>
								<id>default-compile</id>
								<configuration>
									<release>${compiler.module.level}</release>
								</configuration>
							</execution>
							<execution>
								<id>base-compile</id>
								<goals>
									<goal>compile</goal>
								</goals>
								<configuration>
									<excludes>
										<exclude>module-info.java</exclude>
									</excludes>
								</configuration>
							</execution>
						</executions>
						<configuration>
							<source>${compiler.level}</source>
							<target>${compiler.level}</target>
						</configuration>
					</plugin>
					<plugin>
						<groupId>com.github.spotbugs</groupId>
						<artifactId>spotbugs-maven-plugin</artifactId>
						<version>${version.spotbugs-maven-plugin}</version>
						<configuration>
							<effort>Max</effort>
							<threshold>Medium</threshold>
							<includeTests>false</includeTests>
							<failOnError>false</failOnError>
							<plugins>
								<plugin>
									<groupId>com.h3xstream.findsecbugs</groupId>
									<artifactId>findsecbugs-plugin</artifactId>
									<version>${version.findsecbugs-plugin}</version>
								</plugin>
								<plugin>
									<groupId>com.mebigfatguy.sb-contrib</groupId>
									<artifactId>sb-contrib</artifactId>
									<version>${version.sb-contrib}</version>
								</plugin>
							</plugins>
						</configuration>
						<executions>
							<execution>
								<id>spotbugs-check</id>
								<phase>verify</phase>
								<goals>
									<goal>check</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>

	</profiles>

</project>
